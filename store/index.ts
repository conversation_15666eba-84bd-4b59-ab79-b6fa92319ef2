import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import userReducer from './slices/userSlice';
import fineReducer from './slices/fineSlice';
import serviceReducer from './slices/serviceSlice';
import settingsReducer from './slices/settingsSlice';
import paymentMethodsReducer from './slices/paymentMethodsSlice';

// Cấu hình Redux Persist
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  // Chỉ lưu trữ các slice cần thiết
  whitelist: ['fine'], // Chỉ lưu trữ fine slice để giữ lịch sử tìm kiếm
};

// Kết hợp tất cả reducers
const rootReducer = combineReducers({
  user: userReducer,
  fine: fineReducer,
  service: serviceReducer,
  settings: settingsReducer,
  paymentMethods: paymentMethodsReducer,
  // Thêm các reducer khác ở đây khi cần
});

// Tạo persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Tạo store với persisted reducer
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Bỏ qua các action của redux-persist để tránh lỗi serialization
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

// Tạo persistor
export const persistor = persistStore(store);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
