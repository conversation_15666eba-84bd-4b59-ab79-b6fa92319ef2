import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { getPaymentMethods, PaymentMethod } from '@/api/payment-methods.api';

// Define the state interface
interface PaymentMethodsState {
  methods: PaymentMethod[];
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
}

// Define the initial state
const initialState: PaymentMethodsState = {
  methods: [],
  isLoading: false,
  error: null,
  lastFetched: null,
};

// Async thunk for fetching payment methods
export const fetchPaymentMethods = createAsyncThunk(
  'paymentMethods/fetchPaymentMethods',
  async (_, { rejectWithValue }) => {
    try {
      const methods = await getPaymentMethods();
      return methods;
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      return rejectWithValue('Failed to fetch payment methods');
    }
  }
);

// Create the slice
const paymentMethodsSlice = createSlice({
  name: 'paymentMethods',
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    // Reset state
    resetPaymentMethods: (state) => {
      state.methods = [];
      state.isLoading = false;
      state.error = null;
      state.lastFetched = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPaymentMethods.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPaymentMethods.fulfilled, (state, action: PayloadAction<PaymentMethod[]>) => {
        state.isLoading = false;
        state.methods = action.payload;
        state.error = null;
        state.lastFetched = Date.now();
      })
      .addCase(fetchPaymentMethods.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        // Keep existing methods if fetch fails
      });
  },
});

// Export actions
export const { clearError, resetPaymentMethods } = paymentMethodsSlice.actions;

// Export selectors
export const selectPaymentMethods = (state: { paymentMethods: PaymentMethodsState }) => state.paymentMethods.methods;
export const selectPaymentMethodsLoading = (state: { paymentMethods: PaymentMethodsState }) => state.paymentMethods.isLoading;
export const selectPaymentMethodsError = (state: { paymentMethods: PaymentMethodsState }) => state.paymentMethods.error;
export const selectPaymentMethodByCode = (code: string) => (state: { paymentMethods: PaymentMethodsState }) => 
  state.paymentMethods.methods.find(method => method.code === code);

// Export reducer
export default paymentMethodsSlice.reducer;
