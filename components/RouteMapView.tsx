import { RouteInfo } from "@/api/direction.api";
import GoongConstants from "@/constants/GoongConstants";
import {
  Camera,
  LineLayer,
  MapView,
  ShapeSource,
} from "@maplibre/maplibre-react-native";
import React, { useRef, useState } from "react";
import { Alert } from "react-native";

interface RouteMapViewProps {
  coordinates: [number, number];
  routeInfo: RouteInfo | null;
  mapPoints: {
    id: string;
    coordinates: [number, number];
    title: string;
  }[];
}

const RouteMapView: React.FC<RouteMapViewProps> = ({
  coordinates,
  routeInfo,
  mapPoints,
}) => {
  const camera = useRef(null);

  const convertDistanceToZoomLevel = (distance: number) => {
    const distanceZoomMap = [
      { distance: 1000, zoom: 15 },
      { distance: 2000, zoom: 14 },
      { distance: 5000, zoom: 13 },
      { distance: 10000, zoom: 12 },
      { distance: 20000, zoom: 11 },
      { distance: 50000, zoom: 10 },
      { distance: 100000, zoom: 9 },
    ];

    for (const { distance: d, zoom } of distanceZoomMap) {
      if (distance <= d) {
        return zoom;
      }
    }

    return 9;
  };

  const [loadMap] = useState(() => {
    if (
      !GoongConstants ||
      !GoongConstants.MAP_TILES_URL ||
      !GoongConstants.MAP_KEY
    ) {
      return "https://tiles.goong.io/assets/goong_map_web.json?api_key=H32hbWEAgDaXRTJzzT9hxHCU1S0YH9QxjX30SR5S";
    }
    return `${GoongConstants.MAP_TILES_URL}?api_key=${GoongConstants.MAP_KEY}`;
  });

  return (
    <MapView
      mapStyle={loadMap}
      style={{ flex: 1 }}
      zoomEnabled={true}
      logoEnabled={false}
      onDidFailLoadingMap={() => {
        Alert.alert(
          "Lỗi bản đồ",
          "Không thể tải bản đồ. Vui lòng thử lại sau."
        );
      }}
    >
      <Camera
        ref={camera}
        zoomLevel={convertDistanceToZoomLevel(routeInfo?.distance.value || 0)} // Mức thu phóng của bản đồ
        centerCoordinate={coordinates}
        animationDuration={0}
      />

      {/* đường đi */}
      {routeInfo ? (
        <ShapeSource
          id="lineSource"
          shape={{
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: routeInfo.polylineCoordinates,
            },
          }}
        >
          <LineLayer
            id="lineLayer"
            style={{
              lineColor: "green",
              lineWidth: 7,
              lineCap: "round",
              lineJoin: "round",
            }}
          />
        </ShapeSource>
      ) : null}
    </MapView>
  );
};

export default RouteMapView;
