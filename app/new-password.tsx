import { Button } from '@/components/ui/Button';
import { PasswordInput } from '@/components/ui/PasswordInput';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/contexts/AuthContext';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View
} from 'react-native';

export default function NewPasswordScreen() {
  const { setNewPassword } = useAuth();
  const params = useLocalSearchParams<{ phone: string }>();
  const phone = params.phone || '';

  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const validateForm = () => {
    if (!password) {
      setError('<PERSON><PERSON>t khẩu không được để trống');
      return false;
    }

    if (password.length !== 6) {
      setError('Mật khẩu phải có đúng 6 ký tự');
      return false;
    }

    setError('');
    return true;
  };

  const handleSetNewPassword = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const result = await setNewPassword(phone, password);

      if (result) {
        setSuccess(true);
      } else {
        Alert.alert(
          'Lỗi',
          'Không thể đặt lại mật khẩu. Vui lòng thử lại sau.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Set new password error:', error);
      Alert.alert(
        'Lỗi',
        'Đã xảy ra lỗi. Vui lòng thử lại sau.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <StatusBar style="dark" />
      <Stack.Screen
        options={{
          headerTitle: 'Đặt mật khẩu mới',
          headerTitleAlign: 'center',
          headerBackTitle:  "Trở lại"
        }}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('@/assets/images/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          {!success ? (
            <>
              <Text style={styles.subtitle}>
                Tạo mật khẩu mới gồm 6 chữ số cho tài khoản của bạn
              </Text>

              <View style={styles.form}>
                <PasswordInput
                  label="Mật khẩu mới"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    setError('');
                  }}
                  error={error}
                />

                <Button
                  title="Đặt lại mật khẩu"
                  onPress={handleSetNewPassword}
                  loading={isLoading}
                  style={styles.submitButton}
                />
              </View>
            </>
          ) : (
            <View style={styles.successContainer}>
              <View style={styles.successIconContainer}>
                <Text style={styles.successIcon}>✓</Text>
              </View>
              <Text style={styles.successTitle}>
                Đặt lại mật khẩu thành công!
              </Text>
              <Text style={styles.successMessage}>
                Mật khẩu của bạn đã được cập nhật. Bây giờ bạn có thể đăng nhập bằng mật khẩu mới.
              </Text>
              <Button
                title="Đăng nhập"
                onPress={() => router.replace('/login-phone')}
                style={styles.loginButton}
              />
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 120,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subtitle: {
    textAlign: 'center',
    color: '#687076',
    marginBottom: 32,
    lineHeight: 22,
  },
  form: {
    marginBottom: 24,
  },
  submitButton: {
    marginTop: 16,
    color: Colors.white,
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  successIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  successIcon: {
    color: '#fff',
    fontSize: 40,
    fontWeight: 'bold',
  },
  successTitle: {
    marginBottom: 16,
    color: '#4CAF50',
  },
  successMessage: {
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
    color: '#687076',
  },
  loginButton: {
    width: '100%',
  },
});
