import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Text,
} from "react-native";
import { Stack, Link, router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";
import { checkPhoneRegistered } from "@/api/auth.api";

export default function LoginPhoneScreen() {
  const [phone, setPhone] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    if (!phone) {
      setError("Số điện thoại không được để trống");
      return false;
    } else if (!/^[0-9]{10,11}$/.test(phone.replace(/[^0-9]/g, ""))) {
      setError("Số điện thoại không hợp lệ");
      return false;
    }

    setError("");
    return true;
  };

  const handleContinue = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Gọi API kiểm tra số điện thoại
      const response = await checkPhoneRegistered(phone);

      setIsLoading(false);

      if (response.is_registered) {
        // Nếu số điện thoại đã đăng ký, chuyển hướng đến màn hình nhập mật khẩu
        router.push({
          pathname: "/login-password",
          params: { phone },
        });
      } else {
        // Nếu số điện thoại chưa đăng ký, hiển thị thông báo và chuyển hướng đến màn hình đăng ký
        Alert.alert(
          "Thông báo",
          "Số điện thoại này chưa được đăng ký. Bạn có muốn tạo tài khoản mới?",
          [
            {
              text: "Hủy",
              style: "cancel",
            },
            {
              text: "Đăng ký",
              onPress: () => {
                router.push({
                  pathname: "/register",
                  params: { phone },
                });
              },
            },
          ]
        );
      }
    } catch (error) {
      setIsLoading(false);
      console.error("Error checking phone:", error);
      Alert.alert(
        "Lỗi",
        "Đường truyền mạng không ổn định. Vui lòng thử lại sau."
      );
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />
      <Stack.Screen
        options={{
          headerTitle: "Đăng nhập",
          headerTitleAlign: "center",
          headerShown: false,
        }}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.title}>Đăng nhập</Text>
          <Text style={styles.subtitle}>Chào mừng bạn đến với SayGo</Text>

          <View style={styles.form}>
            <Input
              label="Số điện thoại"
              placeholder="Nhập số điện thoại của bạn"
              keyboardType="phone-pad"
              value={phone}
              onChangeText={(text) => {
                setPhone(text);
                setError("");
              }}
              error={error}
              leftIcon="phone"
            />

            <Button
              title="Tiếp tục"
              onPress={handleContinue}
              loading={isLoading}
              style={styles.continueButton}
            />

            {/* <View style={styles.orContainer}>
              <View style={styles.divider} />
              <Text style={styles.orText}>HOẶC</Text>
              <View style={styles.divider} />
            </View> */}

            {/* <View style={styles.socialButtonsContainer}>
              <SocialIconButton
                provider="facebook"
                onPress={() => alert("Đăng nhập bằng Facebook")}
              />

              <SocialIconButton
                provider="google"
                onPress={() => alert("Đăng nhập bằng Google")}
              />

              {Platform.OS === "ios" && (
                <SocialIconButton
                  provider="apple"
                  onPress={() => alert("Đăng nhập bằng Apple")}
                />
              )}
            </View> */}
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Bạn chưa có tài khoản? </Text>
            <Link href="/register" asChild>
              <TouchableOpacity>
                <Text style={styles.registerLink}>Đăng ký ngay</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 60,
    marginBottom: 20,
  },
  logo: {
    width: 300,
    height: 200,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  title: {
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    textAlign: "center",
    color: "#687076",
    marginBottom: 32,
  },
  form: {
    marginBottom: 24,
  },
  continueButton: {
    marginTop: 16,
  },
  orContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "#E6E8EB",
  },
  orText: {
    marginHorizontal: 10,
    color: "#687076",
    fontSize: 12,
    fontWeight: "600",
  },
  socialButtonsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 16,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 16,
    marginBottom: 32,
  },
  footerText: {
    color: "#687076",
  },
  registerLink: {
    color: Colors.primary,
    fontWeight: "600",
  },
});
