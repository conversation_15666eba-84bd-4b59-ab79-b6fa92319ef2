import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
  Alert,
  Text,
} from "react-native";
import { Stack, router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";
import { OtpPurpose, useAuth } from "@/contexts/AuthContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { MaterialIcons } from "@expo/vector-icons";

export default function VerifyPhoneScreen() {
  const { user, verifyOTP, resendOTP } = useAuth();
  const phone = user?.phone || "";

  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [countdown, setCountdown] = useState(0);
  const inputRefs = useRef<Array<TextInput | null>>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Tự động focus vào ô đầu tiên khi màn hình được tải
  useEffect(() => {
    setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 100);
  }, []);

  const handleOtpChange = (text: string, index: number) => {
    // Chỉ cho phép nhập số
    if (!/^[0-9]*$/.test(text)) return;

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);
    setError("");

    // Tự động focus vào ô tiếp theo nếu đã nhập
    if (text.length === 1 && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    // Xử lý khi nhấn phím xóa
    if (e.nativeEvent.key === "Backspace" && index > 0 && otp[index] === "") {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const getOtpString = () => otp.join("");

  const handleVerifyOTP = async () => {
    const otpString = getOtpString();

    if (otpString.length !== 6) {
      setError("Vui lòng nhập đầy đủ mã OTP 6 số");
      return;
    }

    setIsLoading(true);

    try {
      const result = await verifyOTP(phone, otpString, OtpPurpose.VERIFY_PHONE);

      if (result) {
        // OTP verification successful, now sign in the user
        try {
          // set user data in AsyncStorage
          // user.is_phone_confirmed = true
          const userData = {
            ...user,
            is_phone_confirmed: true,
          };
          await AsyncStorage.setItem("user", JSON.stringify(userData));

          // Navigate to the main screen
          router.replace("/(tabs)/account");
        } catch (signInError) {
          console.error("Sign in error:", signInError);
          router.replace("/login-phone");
        }
      } else {
        setError("Mã OTP không chính xác. Vui lòng thử lại.");
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      setError("Đã xảy ra lỗi. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to start the countdown timer
  const startCountdown = () => {
    // Set initial countdown to 60 seconds
    setCountdown(60);

    // Clear any existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Create a new timer that decrements the countdown every second
    timerRef.current = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          // When countdown reaches 0, clear the interval
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prevCount - 1;
      });
    }, 1000);
  };

  const handleResendOTP = async () => {
    // If countdown is still active, don't allow resending
    if (countdown > 0) return;

    setIsLoading(true);
    try {
      // Call the API to resend OTP
      const result = await resendOTP(phone, OtpPurpose.VERIFY_PHONE);

      if (result) {
        // Start the countdown timer
        startCountdown();
      } else {
        // Show error message
        Alert.alert("Lỗi", "Không thể gửi lại mã OTP. Vui lòng thử lại sau.");
      }
    } catch (error) {
      console.error("Resend OTP error:", error);
      Alert.alert(
        "Lỗi",
        "Đã xảy ra lỗi khi gửi lại mã OTP. Vui lòng thử lại sau."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Absolute positioned back button */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => router.back()}
        activeOpacity={0.7}
      >
        <MaterialIcons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.screenTitle}>Xác thực số điện thoại</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.subtitle}>
            Nhập mã OTP 6 số đã được gửi đến số điện thoại {phone}
          </Text>

          <View style={styles.otpContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputRefs.current[index] = ref)}
                style={styles.otpInput}
                value={digit}
                onChangeText={(text) => handleOtpChange(text, index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                keyboardType="number-pad"
                maxLength={1}
                selectTextOnFocus
              />
            ))}
          </View>

          {error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : null}

          <Button
            title="Xác nhận"
            onPress={handleVerifyOTP}
            loading={isLoading}
            style={styles.submitButton}
          />

          <TouchableOpacity
            style={styles.resendButton}
            onPress={handleResendOTP}
            disabled={isLoading || countdown > 0}
          >
            <Text style={styles.resendLabel}>Không nhận được mã?</Text>
            <Text
              style={[styles.resendText, countdown > 0 && styles.disabledText]}
            >
              {countdown > 0 ? `Gửi lại mã (${countdown}s)` : "Gửi lại mã"}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backButton: {
    position: "absolute",
    top: Platform.OS === "ios" ? 50 : 30,
    left: 16,
    zIndex: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    elevation: 3,
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 52,
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 120,
  },
  screenTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
    marginTop: 10,
    marginBottom: 10,
    textAlign: "center",
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subtitle: {
    textAlign: "center",
    color: "#687076",
    marginBottom: 32,
    lineHeight: 22,
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: "#E6E8EB",
    borderRadius: 8,
    textAlign: "center",
    fontSize: 24,
    fontWeight: "bold",
  },
  errorContainer: {
    backgroundColor: "#FFEBEE",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: "#E53935",
  },
  errorText: {
    color: "#E53935",
    fontSize: 14,
  },
  submitButton: {
    marginBottom: 16,
  },
  resendButton: {
    alignItems: "center",
    padding: 8,
  },
  resendText: {
    color: Colors.primary,
    fontWeight: "700",
  },
  resendLabel: {
    color: "#687076",
  },
  cancelButton: {
    alignItems: "center",
    padding: 8,
    marginTop: 16,
  },
  cancelText: {
    color: "#E53935",
    fontWeight: "700",
  },
  disabledText: {
    color: "#9E9E9E",
  },
});
