import api from "./axios";

/**
 * Interface for payment methods
 */
export interface PaymentMethod {
  id: number;
  name: string;
  code: string;
  icon: string;
}

/**
 * Interface for API response
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

/**
 * Get all payment methods
 * @returns Promise with all payment methods
 */
export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  try {
    const response = await api.get("/api/v1/payment-methods");
    return response.data || [];
  } catch (error) {
    console.error("Error fetching payment methods:", error);
    return [];
  }
};
